/* Modern Admin Panel Styles */

:root {
    --primary-color: #667eea;
    --primary-dark: #5a67d8;
    --secondary-color: #6c757d;
    --success-color: #10b981;
    --info-color: #3b82f6;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --light-color: #f8fafc;
    --dark-color: #1e293b;
    --sidebar-width: 280px;
    --sidebar-collapsed-width: 80px;
    --topbar-height: 70px;
    --border-radius: 0.75rem;
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

body {
    font-family: 'Vazirmatn', 'Inter', 'Segoe UI', 'Roboto', sans-serif;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    color: #334155;
    line-height: 1.6;
}

/* Admin Container */
.admin-container {
    display: flex;
    min-height: 100vh;
}

/* Modern Sidebar */
.admin-sidebar {
    width: var(--sidebar-width);
    background: linear-gradient(180deg, #1e293b 0%, #0f172a 100%);
    color: white;
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    z-index: 100;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: var(--shadow-lg);
    backdrop-filter: blur(10px);
    border-left: 1px solid rgba(255, 255, 255, 0.1);
}

.admin-sidebar.collapsed {
    width: var(--sidebar-collapsed-width);
}

.sidebar-header {
    padding: 2rem 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
}

.sidebar-header h3 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 800;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.sidebar-user {
    padding: 1.5rem;
    display: flex;
    align-items: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    background: rgba(255, 255, 255, 0.03);
    margin: 1rem;
    border-radius: var(--border-radius);
    transition: all 0.3s ease;
}

.sidebar-user:hover {
    background: rgba(255, 255, 255, 0.08);
    transform: translateY(-1px);
}

.sidebar-user img {
    width: 50px;
    height: 50px;
    object-fit: cover;
    margin-left: 1rem;
    border-radius: 50%;
    border: 3px solid rgba(102, 126, 234, 0.3);
    transition: all 0.3s ease;
}

.sidebar-user:hover img {
    border-color: rgba(102, 126, 234, 0.6);
    transform: scale(1.05);
}

.sidebar-user h6 {
    margin: 0;
    font-size: 0.95rem;
    font-weight: 600;
    color: #e2e8f0;
}

.sidebar-user .badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 1rem;
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    border: none;
}

.sidebar-nav {
    list-style: none;
    padding: 1rem 0;
    margin: 0;
}

.sidebar-nav .nav-item {
    position: relative;
    margin: 0.25rem 1rem;
}

.sidebar-nav .nav-link {
    display: flex;
    align-items: center;
    padding: 1rem 1.25rem;
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: var(--border-radius);
    position: relative;
    overflow: hidden;
}

.sidebar-nav .nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.sidebar-nav .nav-link:hover {
    color: white;
    background: rgba(255, 255, 255, 0.1);
    transform: translateX(-3px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.sidebar-nav .nav-link:hover::before {
    opacity: 1;
}

.sidebar-nav .nav-item.active .nav-link {
    color: white;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.2) 0%, rgba(118, 75, 162, 0.2) 100%);
    font-weight: 600;
    border-left: 4px solid #667eea;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.sidebar-nav .nav-item.active .nav-link::before {
    opacity: 1;
}

.sidebar-nav .nav-link i {
    margin-left: 1rem;
    font-size: 1.1rem;
    width: 24px;
    text-align: center;
    transition: all 0.3s ease;
}

.sidebar-nav .nav-link:hover i {
    transform: scale(1.1);
}

.sidebar-nav .nav-link span {
    font-weight: 500;
    transition: all 0.3s ease;
}

/* Modern Main Content */
.admin-content {
    flex: 1;
    margin-right: var(--sidebar-width);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    padding: 2rem;
    min-height: 100vh;
}

.admin-sidebar.collapsed + .admin-content {
    margin-right: var(--sidebar-collapsed-width);
}

/* Modern Cards */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-md);
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    overflow: hidden;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.card-header {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
    border-bottom: 1px solid rgba(102, 126, 234, 0.1);
    padding: 1.5rem 1.5rem;
    font-weight: 600;
    color: var(--dark-color);
}

.card-body {
    padding: 1.5rem;
}

/* Modern Stats Cards */
.stat-card {
    border: none;
    border-radius: var(--border-radius);
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--card-color) 0%, var(--card-color-light) 100%);
}

.stat-card.primary {
    --card-color: var(--primary-color);
    --card-color-light: #8b9cf7;
}

.stat-card.success {
    --card-color: var(--success-color);
    --card-color-light: #6ee7b7;
}

.stat-card.info {
    --card-color: var(--info-color);
    --card-color-light: #93c5fd;
}

.stat-card.warning {
    --card-color: var(--warning-color);
    --card-color-light: #fcd34d;
}

.stat-card.danger {
    --card-color: var(--danger-color);
    --card-color-light: #fca5a5;
}

.stat-card .card-body {
    padding: 2rem;
    position: relative;
}

.stat-card .stat-icon {
    font-size: 3rem;
    opacity: 0.1;
    position: absolute;
    top: 1rem;
    left: 1rem;
    color: var(--card-color);
    transition: all 0.3s ease;
}

.stat-card:hover .stat-icon {
    opacity: 0.2;
    transform: scale(1.1);
}

.stat-card .stat-value {
    font-size: 2.5rem;
    font-weight: 800;
    color: var(--card-color);
    margin-bottom: 0.5rem;
    position: relative;
    z-index: 2;
}

.stat-card .stat-label {
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--secondary-color);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    position: relative;
    z-index: 2;
}

/* Modern Tables */
.table-responsive {
    overflow-x: auto;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
}

.table {
    margin-bottom: 0;
}

.table thead th {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-bottom: 2px solid rgba(102, 126, 234, 0.1);
    font-weight: 600;
    color: var(--dark-color);
    padding: 1rem;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.table tbody td {
    padding: 1rem;
    vertical-align: middle;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.table-hover tbody tr {
    transition: all 0.2s ease;
}

.table-hover tbody tr:hover {
    background-color: rgba(102, 126, 234, 0.05);
    transform: scale(1.01);
}

/* Modern Pagination */
.pagination {
    margin-bottom: 0;
}

.page-item.active .page-link {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    border-color: var(--primary-color);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.page-link {
    color: var(--primary-color);
    border-radius: 0.5rem;
    margin: 0 0.125rem;
    transition: all 0.3s ease;
}

.page-link:hover {
    background-color: rgba(102, 126, 234, 0.1);
    transform: translateY(-1px);
}

/* Modern Forms */
.form-control {
    border-radius: 0.5rem;
    border: 2px solid #e2e8f0;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.25rem rgba(102, 126, 234, 0.15);
    transform: translateY(-1px);
}

.btn {
    border-radius: 0.5rem;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    border: none;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(102, 126, 234, 0.4);
}

.btn-success {
    background: linear-gradient(135deg, var(--success-color) 0%, #059669 100%);
    border: none;
}

.btn-danger {
    background: linear-gradient(135deg, var(--danger-color) 0%, #dc2626 100%);
    border: none;
}

.btn-warning {
    background: linear-gradient(135deg, var(--warning-color) 0%, #d97706 100%);
    border: none;
}

.btn-info {
    background: linear-gradient(135deg, var(--info-color) 0%, #2563eb 100%);
    border: none;
}

/* Mobile Responsive */
@media (max-width: 767.98px) {
    .admin-sidebar {
        width: var(--sidebar-width);
        transform: translateX(100%);
        z-index: 1000;
    }

    .admin-sidebar.mobile-open {
        transform: translateX(0);
        box-shadow: -10px 0 30px rgba(0, 0, 0, 0.3);
    }

    .admin-content {
        margin-right: 0;
        padding: 1rem;
    }

    .admin-sidebar.collapsed + .admin-content {
        margin-right: 0;
    }

    .stat-card .card-body {
        padding: 1.5rem;
    }

    .stat-card .stat-value {
        font-size: 2rem;
    }
}

/* Collapsed Sidebar */
@media (min-width: 768px) {
    .admin-sidebar.collapsed .sidebar-header h3,
    .admin-sidebar.collapsed .sidebar-user div,
    .admin-sidebar.collapsed .nav-link span {
        display: none;
    }

    .admin-sidebar.collapsed .sidebar-user {
        justify-content: center;
        margin: 1rem 0.5rem;
    }

    .admin-sidebar.collapsed .sidebar-user img {
        margin-left: 0;
    }

    .admin-sidebar.collapsed .nav-item {
        margin: 0.25rem 0.5rem;
    }

    .admin-sidebar.collapsed .nav-link {
        justify-content: center;
        padding: 1rem;
    }

    .admin-sidebar.collapsed .nav-link i {
        margin-left: 0;
        font-size: 1.25rem;
    }
}

/* Additional Modern Enhancements */
.admin-topbar {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    box-shadow: var(--shadow-sm);
}

.dropdown-menu {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.95);
}

.dropdown-item {
    padding: 0.75rem 1.25rem;
    transition: all 0.2s ease;
}

.dropdown-item:hover {
    background: rgba(102, 126, 234, 0.1);
    transform: translateX(5px);
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid transparent;
    border-top: 2px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

/* Custom Scrollbar */
.admin-sidebar::-webkit-scrollbar {
    width: 6px;
}

.admin-sidebar::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
}

.admin-sidebar::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
}

.admin-sidebar::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
}
