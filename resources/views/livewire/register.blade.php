<div class="auth-container">
    <div class="container-fluid">
        <div class="row min-vh-100">
            <!-- Left Side - Branding -->
            <div class="col-lg-6 d-none d-lg-flex auth-brand-side">
                <div class="auth-brand-content">
                    <div class="text-center">
                        <div class="auth-logo mb-4">
                            <i class="fas fa-user-plus fa-4x text-white mb-3"></i>
                            <h1 class="text-white fw-bold">عضویت در بیست رنگ</h1>
                            <p class="text-white-50 lead">شروع سفر شما در دنیای آگهی</p>
                        </div>

                        <div class="auth-features">
                            <div class="feature-item mb-4">
                                <i class="fas fa-gift text-success fa-2x mb-2"></i>
                                <h5 class="text-white">عضویت رایگان</h5>
                                <p class="text-white-50">بدون هیچ هزینه‌ای عضو شوید</p>
                            </div>
                            <div class="feature-item mb-4">
                                <i class="fas fa-rocket text-info fa-2x mb-2"></i>
                                <h5 class="text-white">شروع سریع</h5>
                                <p class="text-white-50">در کمتر از یک دقیقه آگهی ثبت کنید</p>
                            </div>
                            <div class="feature-item">
                                <i class="fas fa-star text-warning fa-2x mb-2"></i>
                                <h5 class="text-white">امکانات ویژه</h5>
                                <p class="text-white-50">دسترسی به تمام امکانات سایت</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Right Side - Register Form -->
            <div class="col-lg-6 d-flex align-items-center justify-content-center auth-form-side">
                <div class="auth-form-container">
                    <div class="text-center mb-4">
                        <div class="d-lg-none mb-3">
                            <i class="fas fa-bullhorn fa-3x text-primary"></i>
                            <h2 class="text-primary fw-bold mt-2">بیست رنگ</h2>
                        </div>
                        <h3 class="fw-bold text-dark mb-2">ثبت نام کنید</h3>
                        <p class="text-muted">حساب کاربری جدید ایجاد کنید و شروع کنید</p>
                    </div>

                    <form wire:submit.prevent="register" class="auth-form">
                        <div class="mb-4">
                            <label class="form-label fw-semibold" for="mobile">
                                <i class="fas fa-mobile-alt me-2 text-primary"></i>
                                شماره همراه
                            </label>
                            <div class="input-group">
                                <span class="input-group-text bg-light border-end-0">
                                    <i class="fas fa-phone text-muted"></i>
                                </span>
                                <input class="form-control border-start-0 ps-0"
                                       type="text"
                                       wire:model="mobile"
                                       placeholder="09xxxxxxxxx"
                                       dir="ltr"/>
                            </div>
                            @error('mobile')
                                <div class="text-danger small mt-1">
                                    <i class="fas fa-exclamation-circle me-1"></i>{{ $message }}
                                </div>
                            @enderror
                        </div>

                        <div class="mb-4">
                            <label class="form-label fw-semibold" for="password">
                                <i class="fas fa-lock me-2 text-primary"></i>
                                رمز عبور
                            </label>
                            <div class="input-group">
                                <span class="input-group-text bg-light border-end-0">
                                    <i class="fas fa-key text-muted"></i>
                                </span>
                                <input class="form-control border-start-0 ps-0"
                                       type="password"
                                       wire:model="password"
                                       placeholder="حداقل 8 کاراکتر"/>
                            </div>
                            @error('password')
                                <div class="text-danger small mt-1">
                                    <i class="fas fa-exclamation-circle me-1"></i>{{ $message }}
                                </div>
                            @enderror
                        </div>

                        <div class="mb-4">
                            <label class="form-label fw-semibold" for="password_confirmation">
                                <i class="fas fa-shield-alt me-2 text-primary"></i>
                                تکرار رمز عبور
                            </label>
                            <div class="input-group">
                                <span class="input-group-text bg-light border-end-0">
                                    <i class="fas fa-check text-muted"></i>
                                </span>
                                <input class="form-control border-start-0 ps-0"
                                       type="password"
                                       wire:model="password_confirmation"
                                       placeholder="رمز عبور را مجدداً وارد کنید"/>
                            </div>
                            @error('password_confirmation')
                                <div class="text-danger small mt-1">
                                    <i class="fas fa-exclamation-circle me-1"></i>{{ $message }}
                                </div>
                            @enderror
                        </div>

                        <div class="mb-4">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="terms" required>
                                <label class="form-check-label text-muted small" for="terms">
                                    با <a href="#" class="text-decoration-none">قوانین و مقررات</a> و
                                    <a href="#" class="text-decoration-none">حریم خصوصی</a> موافقم
                                </label>
                            </div>
                        </div>

                        <button type="submit" class="btn btn-primary btn-lg w-100 mb-4" wire:loading.attr="disabled">
                            <span wire:loading.remove>
                                <i class="fas fa-user-plus me-2"></i>
                                ایجاد حساب کاربری
                            </span>
                            <span wire:loading>
                                <i class="fas fa-spinner fa-spin me-2"></i>
                                در حال ثبت نام...
                            </span>
                        </button>

                        <div class="text-center">
                            <p class="text-muted mb-0">
                                قبلاً ثبت نام کرده‌اید؟
                                <a href="{{ route('login') }}" class="text-decoration-none fw-semibold">
                                    وارد شوید
                                </a>
                            </p>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@push('styles')
<style>
    .auth-container {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
    }

    .auth-brand-side {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        position: relative;
        overflow: hidden;
    }

    .auth-brand-side::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.1)"/><circle cx="10" cy="60" r="0.5" fill="rgba(255,255,255,0.1)"/><circle cx="90" cy="40" r="0.5" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        opacity: 0.3;
    }

    .auth-brand-content {
        position: relative;
        z-index: 2;
        padding: 2rem;
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100%;
    }

    .auth-logo {
        animation: fadeInUp 1s ease-out;
    }

    .auth-features .feature-item {
        animation: fadeInUp 1s ease-out;
        animation-fill-mode: both;
    }

    .auth-features .feature-item:nth-child(1) { animation-delay: 0.2s; }
    .auth-features .feature-item:nth-child(2) { animation-delay: 0.4s; }
    .auth-features .feature-item:nth-child(3) { animation-delay: 0.6s; }

    .auth-form-side {
        background: #ffffff;
        padding: 2rem;
    }

    .auth-form-container {
        width: 100%;
        max-width: 400px;
        animation: fadeInRight 1s ease-out;
    }

    .auth-form {
        animation: fadeInUp 1s ease-out 0.3s;
        animation-fill-mode: both;
    }

    .input-group-text {
        border-right: 1px solid #dee2e6;
    }

    .form-control:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }

    .btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 0.5rem;
        padding: 0.75rem 1.5rem;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
    }

    .btn-primary:disabled {
        opacity: 0.7;
        transform: none;
    }

    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    @keyframes fadeInRight {
        from {
            opacity: 0;
            transform: translateX(30px);
        }
        to {
            opacity: 1;
            transform: translateX(0);
        }
    }

    @media (max-width: 991.98px) {
        .auth-container {
            background: #ffffff;
        }

        .auth-form-side {
            min-height: 100vh;
        }
    }
</style>
@endpush
