<div class="dropdown" wire:poll.30s="loadNotifications">
    <button class="btn btn-link dropdown-toggle position-relative" type="button" id="notificationsDropdown" 
            data-bs-toggle="dropdown" aria-expanded="false">
        <i class="fas fa-bell"></i>
        @if($unreadCount > 0)
            <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                {{ $unreadCount > 99 ? '99+' : $unreadCount }}
                <span class="visually-hidden">اعلان‌های خوانده نشده</span>
            </span>
        @endif
    </button>
    
    <ul class="dropdown-menu dropdown-menu-end notification-dropdown" aria-labelledby="notificationsDropdown" 
        style="width: 350px; max-height: 400px; overflow-y: auto;">
        
        <!-- Header -->
        <li class="dropdown-header d-flex justify-content-between align-items-center">
            <span>اعلان‌ها</span>
            @if($unreadCount > 0)
                <button class="btn btn-sm btn-link text-primary p-0" wire:click="markAllAsRead">
                    <small>همه را خوانده شده علامت بزن</small>
                </button>
            @endif
        </li>
        
        @if($notifications->count() > 0)
            @foreach($notifications as $notification)
                <li class="notification-item {{ !$notification->is_read ? 'unread' : '' }}">
                    <a class="dropdown-item d-flex align-items-start p-3" 
                       href="{{ $notification->url }}" 
                       wire:click="markAsRead({{ $notification->id }})">
                        <div class="notification-icon me-3">
                            <i class="{{ $notification->icon }} {{ $notification->color_class }}"></i>
                        </div>
                        <div class="notification-content flex-grow-1">
                            <div class="notification-title fw-bold">{{ $notification->title }}</div>
                            <div class="notification-message text-muted small">
                                {{ Str::limit($notification->message, 80) }}
                            </div>
                            <div class="notification-time text-muted small">
                                <i class="fas fa-clock me-1"></i>{{ $notification->time_ago }}
                            </div>
                        </div>
                        @if(!$notification->is_read)
                            <div class="notification-badge">
                                <span class="badge bg-primary rounded-pill">&nbsp;</span>
                            </div>
                        @endif
                    </a>
                    <div class="notification-actions">
                        <button class="btn btn-sm btn-link text-danger" 
                                wire:click.prevent="deleteNotification({{ $notification->id }})"
                                title="حذف اعلان">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </li>
                @if(!$loop->last)
                    <li><hr class="dropdown-divider my-1"></li>
                @endif
            @endforeach
            
            <li><hr class="dropdown-divider"></li>
            
            <!-- Footer Actions -->
            <li class="dropdown-footer text-center p-2">
                @if(!$showAll && $notifications->count() >= 10)
                    <button class="btn btn-sm btn-outline-primary me-2" wire:click="toggleShowAll">
                        مشاهده همه
                    </button>
                @elseif($showAll)
                    <button class="btn btn-sm btn-outline-secondary me-2" wire:click="toggleShowAll">
                        نمایش کمتر
                    </button>
                @endif
                
                <a href="{{ route('admin.notifications.index') }}" class="btn btn-sm btn-primary">
                    مدیریت اعلان‌ها
                </a>
            </li>
        @else
            <li class="text-center py-4">
                <i class="fas fa-bell-slash fa-2x text-muted mb-2"></i>
                <div class="text-muted">هیچ اعلانی وجود ندارد</div>
            </li>
        @endif
    </ul>
</div>

<style>
.notification-dropdown {
    border: none;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    border-radius: 0.5rem;
}

.notification-item {
    position: relative;
    transition: background-color 0.2s ease;
}

.notification-item:hover {
    background-color: #f8f9fa;
}

.notification-item.unread {
    background-color: #e3f2fd;
}

.notification-item.unread:hover {
    background-color: #bbdefb;
}

.notification-item .dropdown-item {
    border: none;
    padding: 0.75rem 1rem;
    white-space: normal;
    word-wrap: break-word;
}

.notification-icon {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f8f9fa;
    border-radius: 50%;
    flex-shrink: 0;
}

.notification-content {
    min-width: 0;
}

.notification-title {
    font-size: 0.875rem;
    line-height: 1.2;
    margin-bottom: 0.25rem;
}

.notification-message {
    font-size: 0.75rem;
    line-height: 1.3;
    margin-bottom: 0.25rem;
}

.notification-time {
    font-size: 0.7rem;
}

.notification-badge {
    flex-shrink: 0;
}

.notification-actions {
    position: absolute;
    top: 0.5rem;
    left: 0.5rem;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.notification-item:hover .notification-actions {
    opacity: 1;
}

.dropdown-header {
    font-weight: 600;
    color: #495057;
    padding: 0.75rem 1rem;
    border-bottom: 1px solid #dee2e6;
}

.dropdown-footer {
    border-top: 1px solid #dee2e6;
    background-color: #f8f9fa;
}

/* Custom scrollbar */
.notification-dropdown::-webkit-scrollbar {
    width: 6px;
}

.notification-dropdown::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.notification-dropdown::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.notification-dropdown::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
</style>
