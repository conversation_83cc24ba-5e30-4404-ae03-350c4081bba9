<div>
    @section('page-title', 'مدیریت پرداخت‌ها')

    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">فیلترها</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="search" class="form-label">جستجو</label>
                                <input type="text" class="form-control" id="search" wire:model.live="search"
                                       placeholder="شناسه تراکنش، کاربر، آگهی...">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="statusFilter" class="form-label">وضعیت</label>
                                <select class="form-select" id="statusFilter" wire:model.live="statusFilter">
                                    <option value="">همه وضعیت‌ها</option>
                                    <option value="pending">در انتظار</option>
                                    <option value="completed">تکمیل شده</option>
                                    <option value="failed">ناموفق</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="userFilter" class="form-label">کاربر</label>
                                <select class="form-select" id="userFilter" wire:model.live="userFilter">
                                    <option value="">همه کاربران</option>
                                    @foreach($users as $user)
                                        <option value="{{ $user->id }}">{{ $user->name ?: $user->mobile }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="perPage" class="form-label">تعداد در صفحه</label>
                                <select class="form-select" id="perPage" wire:model.live="perPage">
                                    <option value="10">10</option>
                                    <option value="25">25</option>
                                    <option value="50">50</option>
                                    <option value="100">100</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">لیست پرداخت‌ها</h5>
                    <div class="d-flex align-items-center">
                        <span class="text-muted me-3">مجموع: {{ $payments->total() }} پرداخت</span>
                    </div>
                </div>
                <div class="card-body p-0">
                    @if($payments->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th>
                                            <button class="btn btn-link p-0 text-decoration-none"
                                                    wire:click="sortBy('id')">
                                                شناسه
                                                @if($sortField === 'id')
                                                    <i class="fas fa-sort-{{ $sortDirection === 'asc' ? 'up' : 'down' }}"></i>
                                                @endif
                                            </button>
                                        </th>
                                        <th>کاربر</th>
                                        <th>آگهی</th>
                                        <th>
                                            <button class="btn btn-link p-0 text-decoration-none"
                                                    wire:click="sortBy('amount')">
                                                مبلغ
                                                @if($sortField === 'amount')
                                                    <i class="fas fa-sort-{{ $sortDirection === 'asc' ? 'up' : 'down' }}"></i>
                                                @endif
                                            </button>
                                        </th>
                                        <th>
                                            <button class="btn btn-link p-0 text-decoration-none"
                                                    wire:click="sortBy('status')">
                                                وضعیت
                                                @if($sortField === 'status')
                                                    <i class="fas fa-sort-{{ $sortDirection === 'asc' ? 'up' : 'down' }}"></i>
                                                @endif
                                            </button>
                                        </th>
                                        <th>شناسه تراکنش</th>
                                        <th>
                                            <button class="btn btn-link p-0 text-decoration-none"
                                                    wire:click="sortBy('created_at')">
                                                تاریخ ایجاد
                                                @if($sortField === 'created_at')
                                                    <i class="fas fa-sort-{{ $sortDirection === 'asc' ? 'up' : 'down' }}"></i>
                                                @endif
                                            </button>
                                        </th>
                                        <th>عملیات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($payments as $payment)
                                        <tr>
                                            <td>{{ $payment->id }}</td>
                                            <td>
                                                @if($payment->user)
                                                    <div>
                                                        <strong>{{ $payment->user->name ?: $payment->user->mobile }}</strong>
                                                        @if($payment->user->mobile)
                                                            <br><small class="text-muted">{{ $payment->user->mobile }}</small>
                                                        @endif
                                                    </div>
                                                @else
                                                    <span class="text-muted">کاربر حذف شده</span>
                                                @endif
                                            </td>
                                            <td>
                                                @if($payment->ad)
                                                    <div>
                                                        <a href="{{ route('ads.show', $payment->ad) }}" target="_blank"
                                                           class="text-decoration-none">
                                                            {{ Str::limit($payment->ad->title, 30) }}
                                                        </a>
                                                        @if($payment->ad->category)
                                                            <br><small class="text-muted">{{ $payment->ad->category->name }}</small>
                                                        @endif
                                                    </div>
                                                @else
                                                    <span class="text-muted">آگهی حذف شده</span>
                                                @endif
                                            </td>
                                            <td>
                                                <strong>{{ number_format($payment->amount) }} تومان</strong>
                                            </td>
                                            <td>
                                                @if($payment->status === 'completed')
                                                    <span class="badge bg-success">تکمیل شده</span>
                                                @elseif($payment->status === 'pending')
                                                    <span class="badge bg-warning">در انتظار</span>
                                                @else
                                                    <span class="badge bg-danger">ناموفق</span>
                                                @endif
                                            </td>
                                            <td>
                                                @if($payment->transaction_id)
                                                    <code>{{ $payment->transaction_id }}</code>
                                                @else
                                                    <span class="text-muted">-</span>
                                                @endif
                                            </td>
                                            <td>
                                                <div>
                                                    {{ $payment->created_at->format('Y/m/d') }}
                                                    <br><small class="text-muted">{{ $payment->created_at->format('H:i') }}</small>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="{{ route('admin.payments.show', $payment) }}"
                                                       class="btn btn-sm btn-outline-primary">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>

                        <div class="card-footer">
                            {{ $payments->links('custom.admin-pagination') }}
                        </div>
                    @else
                        <div class="text-center py-5">
                            <i class="fas fa-credit-card fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">هیچ پرداختی یافت نشد</h5>
                            <p class="text-muted">با تغییر فیلترها دوباره جستجو کنید.</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
