<div>
    @section('page-title', 'جزئیات پرداخت #' . $payment->id)

    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">اطلاعات پرداخت</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">شناسه پرداخت:</label>
                                <p class="mb-0">{{ $payment->id }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">مبلغ:</label>
                                <p class="mb-0 fs-5 text-success">{{ number_format($payment->amount) }} تومان</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">وضعیت:</label>
                                <div>
                                    @if($payment->status === 'completed')
                                        <span class="badge bg-success fs-6">تکمیل شده</span>
                                    @elseif($payment->status === 'pending')
                                        <span class="badge bg-warning fs-6">در انتظار</span>
                                    @else
                                        <span class="badge bg-danger fs-6">ناموفق</span>
                                    @endif
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">تاریخ ایجاد:</label>
                                <p class="mb-0">{{ $payment->created_at->format('Y/m/d H:i') }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">شناسه تراکنش:</label>
                                <p class="mb-0">
                                    @if($payment->transaction_id)
                                        <code>{{ $payment->transaction_id }}</code>
                                    @else
                                        <span class="text-muted">-</span>
                                    @endif
                                </p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">شناسه مرجع:</label>
                                <p class="mb-0">
                                    @if($payment->reference_id)
                                        <code>{{ $payment->reference_id }}</code>
                                    @else
                                        <span class="text-muted">-</span>
                                    @endif
                                </p>
                            </div>
                        </div>
                        @if($payment->description)
                            <div class="col-md-12">
                                <div class="mb-3">
                                    <label class="form-label fw-bold">توضیحات:</label>
                                    <p class="mb-0">{{ $payment->description }}</p>
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
            </div>

            @if($payment->ad)
                <div class="card mt-4">
                    <div class="card-header">
                        <h5 class="mb-0">اطلاعات آگهی</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-12">
                                <div class="mb-3">
                                    <label class="form-label fw-bold">عنوان آگهی:</label>
                                    <p class="mb-0">
                                        <a href="{{ route('ads.show', $payment->ad) }}" target="_blank" 
                                           class="text-decoration-none">
                                            {{ $payment->ad->title }}
                                            <i class="fas fa-external-link-alt ms-1"></i>
                                        </a>
                                    </p>
                                </div>
                            </div>
                            @if($payment->ad->category)
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">دسته‌بندی:</label>
                                        <p class="mb-0">{{ $payment->ad->category->name }}</p>
                                    </div>
                                </div>
                            @endif
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label fw-bold">قیمت آگهی:</label>
                                    <p class="mb-0">{{ number_format($payment->ad->price) }} تومان</p>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="mb-3">
                                    <label class="form-label fw-bold">توضیحات آگهی:</label>
                                    <p class="mb-0">{{ Str::limit($payment->ad->description, 200) }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            @endif
        </div>

        <div class="col-md-4">
            @if($payment->user)
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">اطلاعات کاربر</h5>
                    </div>
                    <div class="card-body">
                        <div class="text-center mb-3">
                            <img src="{{ $payment->user->avatar ? asset('storage/' . $payment->user->avatar) : asset('images/default-avatar.png') }}" 
                                 alt="Avatar" class="rounded-circle" style="width: 80px; height: 80px;">
                        </div>
                        <div class="mb-3">
                            <label class="form-label fw-bold">نام:</label>
                            <p class="mb-0">{{ $payment->user->name ?: 'تعریف نشده' }}</p>
                        </div>
                        <div class="mb-3">
                            <label class="form-label fw-bold">موبایل:</label>
                            <p class="mb-0">{{ $payment->user->mobile ?: 'تعریف نشده' }}</p>
                        </div>
                        <div class="mb-3">
                            <label class="form-label fw-bold">ایمیل:</label>
                            <p class="mb-0">{{ $payment->user->email ?: 'تعریف نشده' }}</p>
                        </div>
                        <div class="mb-3">
                            <label class="form-label fw-bold">وضعیت:</label>
                            <p class="mb-0">
                                @if($payment->user->is_active)
                                    <span class="badge bg-success">فعال</span>
                                @else
                                    <span class="badge bg-danger">غیرفعال</span>
                                @endif
                            </p>
                        </div>
                        <div class="mb-3">
                            <label class="form-label fw-bold">تاریخ عضویت:</label>
                            <p class="mb-0">{{ $payment->user->created_at->format('Y/m/d') }}</p>
                        </div>
                        <div class="d-grid">
                            <a href="{{ route('admin.users.edit', $payment->user) }}" 
                               class="btn btn-outline-primary">
                                <i class="fas fa-edit"></i> ویرایش کاربر
                            </a>
                        </div>
                    </div>
                </div>
            @endif

            @if($payment->status === 'pending')
                <div class="card mt-4">
                    <div class="card-header">
                        <h5 class="mb-0">تغییر وضعیت</h5>
                    </div>
                    <div class="card-body">
                        <p class="text-muted mb-3">می‌توانید وضعیت این پرداخت را تغییر دهید:</p>
                        <div class="d-grid gap-2">
                            <button class="btn btn-success" 
                                    wire:click="updateStatus('completed')"
                                    wire:confirm="آیا از تکمیل این پرداخت اطمینان دارید؟">
                                <i class="fas fa-check"></i> تکمیل پرداخت
                            </button>
                            <button class="btn btn-danger" 
                                    wire:click="updateStatus('failed')"
                                    wire:confirm="آیا از ناموفق بودن این پرداخت اطمینان دارید؟">
                                <i class="fas fa-times"></i> ناموفق
                            </button>
                        </div>
                    </div>
                </div>
            @endif

            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">عملیات</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ route('admin.payments.index') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-right"></i> بازگشت به لیست
                        </a>
                        @if($payment->ad)
                            <a href="{{ route('ads.show', $payment->ad) }}" target="_blank" 
                               class="btn btn-outline-info">
                                <i class="fas fa-eye"></i> مشاهده آگهی
                            </a>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
