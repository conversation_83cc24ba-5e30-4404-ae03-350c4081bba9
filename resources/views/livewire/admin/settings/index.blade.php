<div>
    @section('page-title', 'تنظیمات سایت')

    <div class="row">
        <div class="col-md-12">
            <form wire:submit="save">
                <!-- تنظیمات عمومی -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-cog"></i> تنظیمات عمومی</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="site_name" class="form-label">نام سایت <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('site_name') is-invalid @enderror" 
                                           id="site_name" wire:model="site_name" placeholder="نام سایت">
                                    @error('site_name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="contact_email" class="form-label">ایمیل تماس</label>
                                    <input type="email" class="form-control @error('contact_email') is-invalid @enderror" 
                                           id="contact_email" wire:model="contact_email" placeholder="<EMAIL>">
                                    @error('contact_email')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="contact_phone" class="form-label">تلفن تماس</label>
                                    <input type="text" class="form-control @error('contact_phone') is-invalid @enderror" 
                                           id="contact_phone" wire:model="contact_phone" placeholder="021-12345678">
                                    @error('contact_phone')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="contact_address" class="form-label">آدرس</label>
                                    <input type="text" class="form-control @error('contact_address') is-invalid @enderror" 
                                           id="contact_address" wire:model="contact_address" placeholder="آدرس کامل">
                                    @error('contact_address')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="mb-3">
                                    <label for="site_description" class="form-label">توضیحات سایت</label>
                                    <textarea class="form-control @error('site_description') is-invalid @enderror" 
                                              id="site_description" wire:model="site_description" rows="3" 
                                              placeholder="توضیحات کوتاه درباره سایت"></textarea>
                                    @error('site_description')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="mb-3">
                                    <label for="site_keywords" class="form-label">کلمات کلیدی</label>
                                    <input type="text" class="form-control @error('site_keywords') is-invalid @enderror" 
                                           id="site_keywords" wire:model="site_keywords" 
                                           placeholder="کلمات کلیدی را با کاما جدا کنید">
                                    @error('site_keywords')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- تنظیمات پرداخت -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-credit-card"></i> تنظیمات پرداخت (زرین‌پال)</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-8">
                                <div class="mb-3">
                                    <label for="zarinpal_merchant_id" class="form-label">شناسه پذیرنده زرین‌پال</label>
                                    <input type="text" class="form-control @error('zarinpal_merchant_id') is-invalid @enderror" 
                                           id="zarinpal_merchant_id" wire:model="zarinpal_merchant_id" 
                                           placeholder="xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx">
                                    @error('zarinpal_merchant_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">حالت تست</label>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="zarinpal_sandbox" wire:model="zarinpal_sandbox">
                                        <label class="form-check-label" for="zarinpal_sandbox">
                                            فعال کردن حالت تست (Sandbox)
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- تنظیمات آگهی‌ها -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-bullhorn"></i> تنظیمات آگهی‌ها</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="ads_per_page" class="form-label">تعداد آگهی در صفحه <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control @error('ads_per_page') is-invalid @enderror" 
                                           id="ads_per_page" wire:model="ads_per_page" min="1" max="100">
                                    @error('ads_per_page')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="max_images_per_ad" class="form-label">حداکثر تصاویر هر آگهی <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control @error('max_images_per_ad') is-invalid @enderror" 
                                           id="max_images_per_ad" wire:model="max_images_per_ad" min="1" max="10">
                                    @error('max_images_per_ad')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="max_image_size" class="form-label">حداکثر حجم تصویر (KB) <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control @error('max_image_size') is-invalid @enderror" 
                                           id="max_image_size" wire:model="max_image_size" min="1" max="10240">
                                    @error('max_image_size')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="free_ad_duration" class="form-label">مدت آگهی رایگان (روز) <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control @error('free_ad_duration') is-invalid @enderror" 
                                           id="free_ad_duration" wire:model="free_ad_duration" min="1" max="365">
                                    @error('free_ad_duration')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- قیمت‌گذاری -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-tags"></i> قیمت‌گذاری</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="featured_ad_price" class="form-label">قیمت آگهی ویژه (تومان) <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control @error('featured_ad_price') is-invalid @enderror" 
                                           id="featured_ad_price" wire:model="featured_ad_price" min="0">
                                    @error('featured_ad_price')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="premium_ad_price" class="form-label">قیمت آگهی پریمیوم (تومان) <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control @error('premium_ad_price') is-invalid @enderror" 
                                           id="premium_ad_price" wire:model="premium_ad_price" min="0">
                                    @error('premium_ad_price')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="ladder_ad_price" class="form-label">قیمت آگهی نردبانی (تومان) <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control @error('ladder_ad_price') is-invalid @enderror" 
                                           id="ladder_ad_price" wire:model="ladder_ad_price" min="0">
                                    @error('ladder_ad_price')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- دکمه ذخیره -->
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex justify-content-end">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-save"></i> ذخیره تنظیمات
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
