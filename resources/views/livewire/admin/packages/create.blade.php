<div>
    @section('page-title', 'ایجاد پکیج جدید')

    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">اطلاعات پکیج</h5>
                </div>
                <div class="card-body">
                    <form wire:submit="save">
                        <div class="row">
                            <div class="col-md-12">
                                <div class="mb-3">
                                    <label for="name" class="form-label">نام پکیج <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                           id="name" wire:model="name" placeholder="نام پکیج را وارد کنید">
                                    @error('name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="price" class="form-label">قیمت (تومان) <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control @error('price') is-invalid @enderror" 
                                           id="price" wire:model="price" placeholder="0" min="0">
                                    @error('price')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="duration_days" class="form-label">مدت زمان (روز) <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control @error('duration_days') is-invalid @enderror" 
                                           id="duration_days" wire:model="duration_days" placeholder="30" min="1">
                                    @error('duration_days')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            
                            <div class="col-md-12">
                                <div class="mb-3">
                                    <label for="description" class="form-label">توضیحات</label>
                                    <textarea class="form-control @error('description') is-invalid @enderror" 
                                              id="description" wire:model="description" rows="4" 
                                              placeholder="توضیحات پکیج را وارد کنید"></textarea>
                                    @error('description')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12">
                                <h6 class="mb-3">ویژگی‌های پکیج</h6>
                                
                                <div class="mb-3">
                                    <div class="input-group">
                                        <input type="text" class="form-control" wire:model="newFeature" 
                                               placeholder="ویژگی جدید را وارد کنید" wire:keydown.enter="addFeature">
                                        <button type="button" class="btn btn-outline-primary" wire:click="addFeature">
                                            <i class="fas fa-plus"></i> افزودن
                                        </button>
                                    </div>
                                </div>

                                @if(count($features) > 0)
                                    <div class="mb-3">
                                        <label class="form-label">ویژگی‌های اضافه شده:</label>
                                        <ul class="list-group">
                                            @foreach($features as $index => $feature)
                                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                                    {{ $feature }}
                                                    <button type="button" class="btn btn-sm btn-outline-danger" 
                                                            wire:click="removeFeature({{ $index }})">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </li>
                                            @endforeach
                                        </ul>
                                    </div>
                                @endif
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12">
                                <h6 class="mb-3">تنظیمات</h6>
                                
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-check mb-3">
                                            <input class="form-check-input" type="checkbox" id="is_featured" wire:model="is_featured">
                                            <label class="form-check-label" for="is_featured">
                                                پکیج ویژه
                                            </label>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-4">
                                        <div class="form-check mb-3">
                                            <input class="form-check-input" type="checkbox" id="is_premium" wire:model="is_premium">
                                            <label class="form-check-label" for="is_premium">
                                                پکیج پریمیوم
                                            </label>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-4">
                                        <div class="form-check mb-3">
                                            <input class="form-check-input" type="checkbox" id="is_active" wire:model="is_active">
                                            <label class="form-check-label" for="is_active">
                                                فعال
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="{{ route('admin.packages.index') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-right"></i> بازگشت
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> ذخیره پکیج
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">راهنما</h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle"></i> نکات مهم:</h6>
                        <ul class="mb-0">
                            <li>نام پکیج باید منحصر به فرد باشد</li>
                            <li>قیمت به تومان وارد شود</li>
                            <li>مدت زمان به روز محاسبه می‌شود</li>
                            <li>ویژگی‌ها برای نمایش به کاربران استفاده می‌شود</li>
                        </ul>
                    </div>
                    
                    <div class="alert alert-warning">
                        <h6><i class="fas fa-exclamation-triangle"></i> توجه:</h6>
                        <p class="mb-0">پس از ایجاد پکیج، می‌توانید آن را ویرایش کنید اما تغییرات روی آگهی‌های موجود تأثیر نخواهد گذاشت.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
