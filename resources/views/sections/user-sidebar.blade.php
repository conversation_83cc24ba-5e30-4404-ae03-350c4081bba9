<div class="user-sidebar">
    <!-- User Profile Card -->
    <div class="card user-profile-card mb-4">
        <div class="card-body text-center">
            <div class="user-avatar mb-3">
                <img src="{{ Auth::user()->avatar ? asset('storage/' . Auth::user()->avatar) : asset('images/default-avatar.png') }}"
                     alt="User Avatar" class="rounded-circle">
                <div class="avatar-badge">
                    <i class="fas fa-user"></i>
                </div>
            </div>
            <h5 class="user-name">{{ Auth::user()->name ?: 'کاربر گرامی' }}</h5>
            <p class="user-mobile text-muted">{{ Auth::user()->mobile }}</p>
            <div class="user-stats">
                <div class="stat-item">
                    <span class="stat-number">{{ Auth::user()->ads()->count() }}</span>
                    <span class="stat-label">آگهی</span>
                </div>
                <div class="stat-divider"></div>
                <div class="stat-item">
                    <span class="stat-number">{{ Auth::user()->ads()->where('is_active', true)->count() }}</span>
                    <span class="stat-label">فعال</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Navigation Menu -->
    <div class="card user-nav-card mb-4">
        <div class="card-header">
            <h6 class="mb-0">
                <i class="fas fa-bars me-2"></i>
                منوی کاربری
            </h6>
        </div>
        <div class="card-body p-0">
            <div class="user-nav-menu">
                <a href="{{ route('user.dashboard') }}" class="nav-item {{ request()->routeIs('user.dashboard') ? 'active' : '' }}">
                    <div class="nav-icon">
                        <i class="fas fa-tachometer-alt"></i>
                    </div>
                    <span class="nav-text">داشبورد</span>
                    <div class="nav-arrow">
                        <i class="fas fa-chevron-left"></i>
                    </div>
                </a>

                <a href="{{ route('user.profile') }}" class="nav-item {{ request()->routeIs('user.profile') ? 'active' : '' }}">
                    <div class="nav-icon">
                        <i class="fas fa-user"></i>
                    </div>
                    <span class="nav-text">پروفایل من</span>
                    <div class="nav-arrow">
                        <i class="fas fa-chevron-left"></i>
                    </div>
                </a>

                <a href="{{ route('user.ads') }}" class="nav-item {{ request()->routeIs('user.ads') ? 'active' : '' }}">
                    <div class="nav-icon">
                        <i class="fas fa-bullhorn"></i>
                    </div>
                    <span class="nav-text">آگهی‌های من</span>
                    <div class="nav-arrow">
                        <i class="fas fa-chevron-left"></i>
                    </div>
                </a>

                <a href="{{ route('ads.create') }}" class="nav-item special {{ request()->routeIs('ads.create') ? 'active' : '' }}">
                    <div class="nav-icon">
                        <i class="fas fa-plus"></i>
                    </div>
                    <span class="nav-text">ثبت آگهی جدید</span>
                    <div class="nav-arrow">
                        <i class="fas fa-chevron-left"></i>
                    </div>
                </a>

                <a href="{{ route('user.payments') }}" class="nav-item {{ request()->routeIs('user.payments') ? 'active' : '' }}">
                    <div class="nav-icon">
                        <i class="fas fa-credit-card"></i>
                    </div>
                    <span class="nav-text">پرداخت‌های من</span>
                    <div class="nav-arrow">
                        <i class="fas fa-chevron-left"></i>
                    </div>
                </a>

                <div class="nav-divider"></div>

                <a href="{{ route('logout') }}" class="nav-item logout">
                    <div class="nav-icon">
                        <i class="fas fa-sign-out-alt"></i>
                    </div>
                    <span class="nav-text">خروج از حساب</span>
                    <div class="nav-arrow">
                        <i class="fas fa-chevron-left"></i>
                    </div>
                </a>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="card quick-actions-card">
        <div class="card-header">
            <h6 class="mb-0">
                <i class="fas fa-bolt me-2"></i>
                دسترسی سریع
            </h6>
        </div>
        <div class="card-body">
            <div class="quick-actions">
                <a href="{{ route('ads.create') }}" class="quick-action primary">
                    <i class="fas fa-plus"></i>
                    <span>ثبت آگهی</span>
                </a>
                <a href="#" class="quick-action success">
                    <i class="fas fa-question-circle"></i>
                    <span>راهنما</span>
                </a>
                <a href="#" class="quick-action warning">
                    <i class="fas fa-headset"></i>
                    <span>پشتیبانی</span>
                </a>
                <a href="#" class="quick-action info">
                    <i class="fas fa-chart-line"></i>
                    <span>آمار</span>
                </a>
            </div>
        </div>
    </div>
</div>

@push('styles')
<style>
    .user-sidebar {
        position: sticky;
        top: 2rem;
    }

    .user-profile-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
    }

    .user-avatar {
        position: relative;
        display: inline-block;
    }

    .user-avatar img {
        width: 80px;
        height: 80px;
        object-fit: cover;
        border: 4px solid rgba(255, 255, 255, 0.3);
        transition: all 0.3s ease;
    }

    .user-avatar:hover img {
        transform: scale(1.05);
        border-color: rgba(255, 255, 255, 0.6);
    }

    .avatar-badge {
        position: absolute;
        bottom: 0;
        right: 0;
        width: 24px;
        height: 24px;
        background: #10b981;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        border: 2px solid white;
        font-size: 0.75rem;
    }

    .user-name {
        font-weight: 700;
        margin-bottom: 0.25rem;
    }

    .user-mobile {
        font-size: 0.875rem;
        opacity: 0.8;
        margin-bottom: 1rem;
    }

    .user-stats {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 1rem;
    }

    .stat-item {
        text-align: center;
    }

    .stat-number {
        display: block;
        font-size: 1.5rem;
        font-weight: 800;
        line-height: 1;
    }

    .stat-label {
        font-size: 0.75rem;
        opacity: 0.8;
    }

    .stat-divider {
        width: 1px;
        height: 30px;
        background: rgba(255, 255, 255, 0.3);
    }

    .user-nav-card {
        border: none;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    }

    .user-nav-card .card-header {
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        border-bottom: 1px solid rgba(102, 126, 234, 0.1);
        font-weight: 600;
        color: #334155;
    }

    .user-nav-menu {
        padding: 0;
    }

    .nav-item {
        display: flex;
        align-items: center;
        padding: 1rem 1.25rem;
        color: #64748b;
        text-decoration: none;
        transition: all 0.3s ease;
        border-bottom: 1px solid #f1f5f9;
        position: relative;
    }

    .nav-item:hover {
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
        color: #667eea;
        transform: translateX(-3px);
    }

    .nav-item.active {
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
        color: #667eea;
        font-weight: 600;
        border-left: 4px solid #667eea;
    }

    .nav-item.special {
        background: linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(5, 150, 105, 0.1) 100%);
        color: #10b981;
    }

    .nav-item.special:hover {
        background: linear-gradient(135deg, rgba(16, 185, 129, 0.15) 0%, rgba(5, 150, 105, 0.15) 100%);
    }

    .nav-item.logout {
        color: #ef4444;
    }

    .nav-item.logout:hover {
        background: rgba(239, 68, 68, 0.1);
        color: #dc2626;
    }

    .nav-icon {
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgba(102, 126, 234, 0.1);
        border-radius: 0.5rem;
        margin-left: 1rem;
        transition: all 0.3s ease;
    }

    .nav-item:hover .nav-icon {
        background: rgba(102, 126, 234, 0.2);
        transform: scale(1.1);
    }

    .nav-item.special .nav-icon {
        background: rgba(16, 185, 129, 0.1);
    }

    .nav-item.logout .nav-icon {
        background: rgba(239, 68, 68, 0.1);
    }

    .nav-text {
        flex: 1;
        font-weight: 500;
    }

    .nav-arrow {
        opacity: 0.5;
        transition: all 0.3s ease;
    }

    .nav-item:hover .nav-arrow {
        opacity: 1;
        transform: translateX(-3px);
    }

    .nav-divider {
        height: 1px;
        background: linear-gradient(90deg, transparent 0%, #e2e8f0 50%, transparent 100%);
        margin: 0.5rem 0;
    }

    .quick-actions-card {
        border: none;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    }

    .quick-actions-card .card-header {
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        border-bottom: 1px solid rgba(102, 126, 234, 0.1);
        font-weight: 600;
        color: #334155;
    }

    .quick-actions {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 0.75rem;
    }

    .quick-action {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 1rem;
        border-radius: 0.75rem;
        text-decoration: none;
        transition: all 0.3s ease;
        border: 2px solid transparent;
    }

    .quick-action i {
        font-size: 1.5rem;
        margin-bottom: 0.5rem;
    }

    .quick-action span {
        font-size: 0.75rem;
        font-weight: 600;
    }

    .quick-action.primary {
        background: rgba(102, 126, 234, 0.1);
        color: #667eea;
    }

    .quick-action.primary:hover {
        background: rgba(102, 126, 234, 0.2);
        border-color: #667eea;
        transform: translateY(-2px);
    }

    .quick-action.success {
        background: rgba(16, 185, 129, 0.1);
        color: #10b981;
    }

    .quick-action.success:hover {
        background: rgba(16, 185, 129, 0.2);
        border-color: #10b981;
        transform: translateY(-2px);
    }

    .quick-action.warning {
        background: rgba(245, 158, 11, 0.1);
        color: #f59e0b;
    }

    .quick-action.warning:hover {
        background: rgba(245, 158, 11, 0.2);
        border-color: #f59e0b;
        transform: translateY(-2px);
    }

    .quick-action.info {
        background: rgba(59, 130, 246, 0.1);
        color: #3b82f6;
    }

    .quick-action.info:hover {
        background: rgba(59, 130, 246, 0.2);
        border-color: #3b82f6;
        transform: translateY(-2px);
    }

    @media (max-width: 768px) {
        .user-sidebar {
            position: static;
        }

        .user-stats {
            gap: 0.5rem;
        }

        .quick-actions {
            grid-template-columns: 1fr 1fr 1fr 1fr;
        }

        .quick-action {
            padding: 0.75rem 0.5rem;
        }

        .quick-action i {
            font-size: 1.25rem;
        }

        .quick-action span {
            font-size: 0.7rem;
        }
    }
</style>
@endpush
