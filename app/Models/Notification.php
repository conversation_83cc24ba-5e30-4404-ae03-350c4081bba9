<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class Notification extends Model
{
    use HasFactory;

    protected $fillable = [
        'type',
        'title',
        'message',
        'data',
        'is_read',
        'read_at',
    ];

    protected $casts = [
        'data' => 'array',
        'is_read' => 'boolean',
        'read_at' => 'datetime',
    ];

    /**
     * Scope for unread notifications
     */
    public function scopeUnread($query)
    {
        return $query->where('is_read', false);
    }

    /**
     * Scope for read notifications
     */
    public function scopeRead($query)
    {
        return $query->where('is_read', true);
    }

    /**
     * Scope for recent notifications
     */
    public function scopeRecent($query, $days = 7)
    {
        return $query->where('created_at', '>=', Carbon::now()->subDays($days));
    }

    /**
     * Mark notification as read
     */
    public function markAsRead()
    {
        $this->update([
            'is_read' => true,
            'read_at' => now(),
        ]);
    }

    /**
     * Mark notification as unread
     */
    public function markAsUnread()
    {
        $this->update([
            'is_read' => false,
            'read_at' => null,
        ]);
    }

    /**
     * Get the icon for the notification type
     */
    public function getIconAttribute()
    {
        return match($this->type) {
            'new_ad' => 'fas fa-bullhorn',
            'new_payment' => 'fas fa-credit-card',
            'new_user' => 'fas fa-user-plus',
            'ad_report' => 'fas fa-flag',
            'payment_completed' => 'fas fa-check-circle',
            'payment_failed' => 'fas fa-times-circle',
            'ad_expired' => 'fas fa-clock',
            'new_message' => 'fas fa-envelope',
            default => 'fas fa-bell',
        };
    }

    /**
     * Get the color class for the notification type
     */
    public function getColorClassAttribute()
    {
        return match($this->type) {
            'new_ad' => 'text-primary',
            'new_payment', 'payment_completed' => 'text-success',
            'new_user' => 'text-info',
            'ad_report', 'payment_failed' => 'text-danger',
            'ad_expired' => 'text-warning',
            'new_message' => 'text-secondary',
            default => 'text-muted',
        };
    }

    /**
     * Get the URL for the notification
     */
    public function getUrlAttribute()
    {
        $data = $this->data ?? [];
        
        return match($this->type) {
            'new_ad' => isset($data['ad_id']) ? route('admin.ads.edit', $data['ad_id']) : route('admin.ads.index'),
            'new_payment', 'payment_completed', 'payment_failed' => isset($data['payment_id']) ? route('admin.payments.show', $data['payment_id']) : route('admin.payments.index'),
            'new_user' => isset($data['user_id']) ? route('admin.users.edit', $data['user_id']) : route('admin.users.index'),
            'ad_report' => isset($data['ad_id']) ? route('admin.ads.edit', $data['ad_id']) : route('admin.ads.index'),
            'ad_expired' => isset($data['ad_id']) ? route('admin.ads.edit', $data['ad_id']) : route('admin.ads.index'),
            'new_message' => '#', // You can implement message system later
            default => route('admin.dashboard'),
        };
    }

    /**
     * Create a new notification
     */
    public static function createNotification($type, $title, $message, $data = [])
    {
        return static::create([
            'type' => $type,
            'title' => $title,
            'message' => $message,
            'data' => $data,
        ]);
    }

    /**
     * Get time ago in Persian
     */
    public function getTimeAgoAttribute()
    {
        $diff = $this->created_at->diffInMinutes(now());
        
        if ($diff < 1) {
            return 'همین الان';
        } elseif ($diff < 60) {
            return $diff . ' دقیقه پیش';
        } elseif ($diff < 1440) {
            $hours = floor($diff / 60);
            return $hours . ' ساعت پیش';
        } elseif ($diff < 10080) {
            $days = floor($diff / 1440);
            return $days . ' روز پیش';
        } else {
            return $this->created_at->format('Y/m/d');
        }
    }
}
