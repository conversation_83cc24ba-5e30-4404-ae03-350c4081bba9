<?php

namespace App\Livewire\Admin\Notifications;

use App\Models\Notification;
use Livewire\Component;
use Livewire\WithPagination;

class Index extends Component
{
    use WithPagination;

    public $search = '';
    public $typeFilter = '';
    public $statusFilter = '';
    public $sortField = 'created_at';
    public $sortDirection = 'desc';
    public $perPage = 15;
    public $selectedNotifications = [];
    public $selectAll = false;

    protected $queryString = [
        'search' => ['except' => ''],
        'typeFilter' => ['except' => ''],
        'statusFilter' => ['except' => ''],
        'sortField' => ['except' => 'created_at'],
        'sortDirection' => ['except' => 'desc'],
        'perPage' => ['except' => 15],
    ];

    public function updatingSearch()
    {
        $this->resetPage();
    }

    public function updatingTypeFilter()
    {
        $this->resetPage();
    }

    public function updatingStatusFilter()
    {
        $this->resetPage();
    }

    public function sortBy($field)
    {
        if ($this->sortField === $field) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortDirection = 'asc';
        }

        $this->sortField = $field;
    }

    public function updatedSelectAll()
    {
        if ($this->selectAll) {
            $this->selectedNotifications = $this->getNotifications()->pluck('id')->toArray();
        } else {
            $this->selectedNotifications = [];
        }
    }

    public function markAsRead($notificationId)
    {
        $notification = Notification::find($notificationId);
        if ($notification) {
            $notification->markAsRead();
            session()->flash('success', 'اعلان به عنوان خوانده شده علامت‌گذاری شد.');
        }
    }

    public function markAsUnread($notificationId)
    {
        $notification = Notification::find($notificationId);
        if ($notification) {
            $notification->markAsUnread();
            session()->flash('success', 'اعلان به عنوان خوانده نشده علامت‌گذاری شد.');
        }
    }

    public function deleteNotification($notificationId)
    {
        $notification = Notification::find($notificationId);
        if ($notification) {
            $notification->delete();
            session()->flash('success', 'اعلان با موفقیت حذف شد.');
        }
    }

    public function bulkMarkAsRead()
    {
        if (!empty($this->selectedNotifications)) {
            Notification::whereIn('id', $this->selectedNotifications)->update([
                'is_read' => true,
                'read_at' => now(),
            ]);
            $this->selectedNotifications = [];
            $this->selectAll = false;
            session()->flash('success', 'اعلان‌های انتخاب شده به عنوان خوانده شده علامت‌گذاری شدند.');
        }
    }

    public function bulkDelete()
    {
        if (!empty($this->selectedNotifications)) {
            Notification::whereIn('id', $this->selectedNotifications)->delete();
            $this->selectedNotifications = [];
            $this->selectAll = false;
            session()->flash('success', 'اعلان‌های انتخاب شده با موفقیت حذف شدند.');
        }
    }

    public function markAllAsRead()
    {
        Notification::unread()->update([
            'is_read' => true,
            'read_at' => now(),
        ]);
        session()->flash('success', 'تمام اعلان‌ها به عنوان خوانده شده علامت‌گذاری شدند.');
    }

    public function deleteAllRead()
    {
        Notification::read()->delete();
        session()->flash('success', 'تمام اعلان‌های خوانده شده حذف شدند.');
    }

    private function getNotifications()
    {
        $query = Notification::query();

        if ($this->search) {
            $query->where(function($q) {
                $q->where('title', 'like', '%' . $this->search . '%')
                  ->orWhere('message', 'like', '%' . $this->search . '%');
            });
        }

        if ($this->typeFilter) {
            $query->where('type', $this->typeFilter);
        }

        if ($this->statusFilter) {
            if ($this->statusFilter === 'read') {
                $query->where('is_read', true);
            } elseif ($this->statusFilter === 'unread') {
                $query->where('is_read', false);
            }
        }

        return $query->orderBy($this->sortField, $this->sortDirection);
    }

    public function render()
    {
        $notifications = $this->getNotifications()->paginate($this->perPage);
        
        $stats = [
            'total' => Notification::count(),
            'unread' => Notification::unread()->count(),
            'today' => Notification::whereDate('created_at', today())->count(),
            'this_week' => Notification::where('created_at', '>=', now()->startOfWeek())->count(),
        ];

        $notificationTypes = [
            'new_ad' => 'آگهی جدید',
            'new_payment' => 'پرداخت جدید',
            'new_user' => 'کاربر جدید',
            'ad_report' => 'گزارش آگهی',
            'payment_completed' => 'پرداخت موفق',
            'payment_failed' => 'پرداخت ناموفق',
            'ad_expired' => 'آگهی منقضی',
            'new_message' => 'پیام جدید',
        ];

        return view('livewire.admin.notifications.index', [
            'notifications' => $notifications,
            'stats' => $stats,
            'notificationTypes' => $notificationTypes,
        ])->layout('layouts.admin');
    }
}
