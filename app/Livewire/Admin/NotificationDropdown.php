<?php

namespace App\Livewire\Admin;

use App\Models\Notification;
use Livewire\Component;

class NotificationDropdown extends Component
{
    public $notifications = [];
    public $unreadCount = 0;
    public $showAll = false;

    public function mount()
    {
        $this->loadNotifications();
    }

    public function loadNotifications()
    {
        // Get recent notifications (last 30 days)
        $query = Notification::recent(30)->orderBy('created_at', 'desc');
        
        if (!$this->showAll) {
            $query->limit(10);
        }
        
        $this->notifications = $query->get();
        $this->unreadCount = Notification::unread()->count();
    }

    public function markAsRead($notificationId)
    {
        $notification = Notification::find($notificationId);
        if ($notification) {
            $notification->markAsRead();
            $this->loadNotifications();
        }
    }

    public function markAllAsRead()
    {
        Notification::unread()->update([
            'is_read' => true,
            'read_at' => now(),
        ]);
        $this->loadNotifications();
    }

    public function toggleShowAll()
    {
        $this->showAll = !$this->showAll;
        $this->loadNotifications();
    }

    public function deleteNotification($notificationId)
    {
        $notification = Notification::find($notificationId);
        if ($notification) {
            $notification->delete();
            $this->loadNotifications();
        }
    }

    public function render()
    {
        return view('livewire.admin.notification-dropdown');
    }
}
