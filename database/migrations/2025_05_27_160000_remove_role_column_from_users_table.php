<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Remove the conflicting 'role' string column
            // We keep 'role_id' for the relationship with roles table
            if (Schema::hasColumn('users', 'role')) {
                $table->dropColumn('role');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Add back the role column if needed for rollback
            if (!Schema::hasColumn('users', 'role')) {
                $table->string('role')->default('user')->after('mobile_verified_at');
            }
        });
    }
};
